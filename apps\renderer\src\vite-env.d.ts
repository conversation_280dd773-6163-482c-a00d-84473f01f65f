/// <reference types="vite/client" />
import type { BotStatus, TradeResult, TradingSettings as BotSettings } from '../../shared/types'
// Add other status properties as needed

interface TradeResult {
	success: boolean
	amount: number
	// Add other result properties as needed
}

interface BrowserStatus {
	initialized: boolean
	loggedIn: boolean
}

interface BotSettings {
	// Define your settings structure here
	amount: number
	strategy: string
	// Add other settings as needed
}

interface ApiResponse {
	success: boolean
	message: string
}

interface LoginResponse extends ApiResponse {
	loggedIn: boolean
}

declare global {
	interface Window {
		electronAPI: {
			getSettings: () => Promise<BotSettings>
			saveSettings: (settings: BotSettings) => Promise<boolean>
			startBot: (settings: BotSettings) => Promise<ApiResponse>
			stopBot: () => Promise<ApiResponse>
			getBotStatus: () => Promise<BotStatus>
			updateBotSettings: (settings: BotSettings) => Promise<ApiResponse>
			initializeBrowser: () => Promise<ApiResponse>
			checkLoginStatus: () => Promise<LoginResponse>
			closeBrowser: () => Promise<ApiResponse>
			getBrowserStatus: () => Promise<BrowserStatus>
			onBotStatusUpdate: (callback: (status: BotStatus) => void) => void
			onPriceUpdate: (callback: (price: number) => void) => void
			onTradeResult: (callback: (result: TradeResult) => void) => void
			onStopLossTriggered: (callback: (data: { totalProfit: number; stopLossConfig?: StopLossConfig }) => void) => void
			onBotStatusMessage: (callback: (message: string) => void) => void
			showErrorDialog: (title: string, content: string) => void
			showInfoDialog: (title: string, content: string) => void
			removeAllListeners: (channel: string) => void
			debugPriceElements: () => Promise<unknown>
			selectTradingAsset: () => Promise<unknown>
		}
	}
}

export {}
